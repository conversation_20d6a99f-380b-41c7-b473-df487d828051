import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";
import { ContractDTO } from "./ContractDTO";

export interface AnnexPayrollSummaryDTO {
  contractNumber?: string;
  annexPayrollNumber?: string;
  structureLevelId: string;
  structureLevelName: string;
  position?: NomenclatureDTO;
  contractReason?: NomenclatureDTO;
  permanentContractType?: NomenclatureDTO;
  incomeType?: NomenclatureDTO;
  contractType: ContractDTO;
  dailyWorktime: number;
  workplace: string;
  fromDate: Date;
  toDate: Date;
  lastModifyDate: Date | null;
  contractTermDate: Date | null;
  contractDate: Date | null;
  contractEndDate: Date | null;
  contractTerminationDate: Date | null;
  kid: string;
  ekatte: string;
  nkpd: string;
}
