﻿using MediatR;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Notifications.Hospitals;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Notifications.Absences.MainHandlers
{
    public class AbsenceStatusChangedNotificationHandler(
        IAbsencesService absencesService,
        IMediator mediator)
        : INotificationHandler<AbsenceStatusChangedNotification>
    {
        public async Task Handle(AbsenceStatusChangedNotification notification, CancellationToken cancellationToken)
        {
            var absence = notification.Payload;
            var tasks = new List<Task>();
            var employeeDTO = await absencesService.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital);

            var companyId = notification.CompanyId;
            var creatorId = notification.UserId;
            var creatorName = notification.CreatorName;

            if (!absence.IsHospital)
            {
                var notificationToPublish = (notification.OldAbsence.Status, absence.Status) switch
                {
                    (AbsenceStatus.DeletedByUserAfterApproval, AbsenceStatus.DeletedByAdmin) =>
                       new AbsenceDeletedNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.DeletedByUserAfterApproval, AbsenceStatus.Approved) =>
                        new AbsenceDeleteDeclinedNotificaton(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.EditedByEmployee, AbsenceStatus.Approved) =>
                        new AbsenceEditConfirmNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.EditedByEmployee, AbsenceStatus.Declined) =>
                        new AbsenceEditDeclinedNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.Pending, AbsenceStatus.Approved) =>
                        new AbsenceConfirmNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.Pending, AbsenceStatus.Declined) =>
                        (INotification)new AbsenceDeclinedNotification(absence, companyId, creatorId, creatorName),

                    _ => null
                };

                if (notificationToPublish != null)
                    mediator.Publish(notificationToPublish, cancellationToken);
            }
            else
            {
                var notificationToPublish = (notification.OldAbsence.Status, absence.Status) switch
                {
                    (AbsenceStatus.DeletedByUserAfterApproval, AbsenceStatus.Approved) =>
                        new AbsenceDeleteDeclinedNotificaton(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.DeletedByUserAfterApproval, AbsenceStatus.DeletedByAdmin) =>
                        new AbsenceDeletedNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.EditedByEmployee, AbsenceStatus.Approved) =>
                        new AbsenceEditConfirmNotification(absence, companyId, creatorId, creatorName),

                    (AbsenceStatus.Pending, AbsenceStatus.Approved) =>
                       (INotification)new HospitalConfirmNotification(absence, companyId, creatorId, creatorName),

                    _ => null
                };

                if (notificationToPublish != null)
                    mediator.Publish(notificationToPublish, cancellationToken);

            }

            await Task.WhenAll(tasks);
        }
    }
}