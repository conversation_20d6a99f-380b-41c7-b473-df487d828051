﻿using AutoMapper;
using Gateway.Requests.UserRegistration;
using Microinvest.TransferFiles.Tools.Models.Companies;
using Microinvest.TransferFiles.Tools.Models.Users;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Requests.Companies;

namespace Gateway.Mappers
{
	public class MapperProfile : Profile
	{
		public MapperProfile()
		{
			CreateMap<RegistrationRequest, RegistrationWithTemplateDTO>()
				.ForMember(dest => dest.TemplateName, src => src.MapFrom(_ => "_ConfirmEmailWorktime"));

			CreateMap<ConfirmEmailRequest, ConfirmEmailDTO>();

			CreateMap<CreateNewCompanyRequest, CompanyDTO>();

            CreateMap<EditCompanyRequest, CompanyDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.UserRegistrationCompanyId))
				.ReverseMap()
                .ForMember(dest => dest.UserRegistrationCompanyId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Id, opt => opt.Ignore());

			CreateMap<EmployeeDTO, UserDTO>()
				.ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.WorkTimeId.ToString()))
                .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.EGN));
        }
	}
}
