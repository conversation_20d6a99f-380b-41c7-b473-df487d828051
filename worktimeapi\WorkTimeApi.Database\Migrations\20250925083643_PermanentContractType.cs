﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class PermanentContractType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PermanentContractType",
                schema: "trz",
                table: "Payrolls",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PermanentContractType",
                table: "Payrolls",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PermanentContractType",
                schema: "trz",
                table: "AnnexPayrolls",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PermanentContractType",
                table: "AnnexPayrolls",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PermanentContractType",
                schema: "trz",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "PermanentContractType",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "PermanentContractType",
                schema: "trz",
                table: "AnnexPayrolls");

            migrationBuilder.DropColumn(
                name: "PermanentContractType",
                table: "AnnexPayrolls");
        }
    }
}
