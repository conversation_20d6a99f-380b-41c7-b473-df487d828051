import { useState } from "react";
import styled from "styled-components";
import Container from "../../../../components/Container";
import Fieldset from "../../../../components/Fiedlset/Fieldset";
import Legend from "../../../../components/Fiedlset/Legend";
import Label from "../../../../components/Inputs/Label";
import AdditionalTerms from "../../../../components/payrollViewComponents/AdditionalTerms";
import FieldRow from "../../../../components/payrollViewComponents/FieldRow";
import { AnnexPayrollSummaryDTO } from "../../../../models/DTOs/payrolls/AnnexPayrollSummaryDTO";
import { LengthOfServiceDTO } from "../../../../models/DTOs/payrolls/LengthOfService";
import { PayrollSummaryDTO } from "../../../../models/DTOs/payrolls/PayrollSummaryDTO";
import { translate } from "../../../../services/language/Translator";
import { formatDate, formatExperience } from "../../../../utils/payrollUtils";

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 30%;
  gap: 2rem;
  align-items: flex-start;
`;

const ResponsiveGridContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  height: 100%;
  width: 100%;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(2, minmax(400px, 1fr));
  }
`;

const CustomLayoutContainer = styled(Container)`
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  grid-template-rows: auto auto;
  gap: 1rem;
  height: 100%;
  width: 100%;

  & > :nth-child(1) {
    grid-column: 1;
    grid-row: 1 / span 2;
  }

  & > :nth-child(2) {
    grid-column: 2;
    grid-row: 1;
  }

  & > :nth-child(3) {
    grid-column: 2;
    grid-row: 2;
  }
`;

const RightColumn = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 17%;
  max-height: 20rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  border-radius: 1.2rem;
  justify-content: flex-start;
  align-items: flex-start;
  overflow-y: auto;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 1px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
`;

const MenuText = styled(Label)<{ $isSelected?: boolean }>`
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  margin-left: 1rem;
  text-align: left;
  color: ${(props) =>
    props.$isSelected ? "var(--profile-menu-text-hover-color)" : "#bdc4d6"};
  cursor: pointer;
  width: 100%;
  user-select: none;
`;

const MenuItem = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0.5rem;
  padding-top: 0;
  align-items: flex-start;

  &:hover {
    ${MenuText} {
      color: var(--profile-department-name-font-color);
    }
  }
`;

const MenuLine = styled.div`
  width: 100%;
  height: 1px;
  background-color: #bdc4d6;
  opacity: 0.5;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

// Helper function to calculate experience from date range
const calculateExperienceFromDates = (
  fromDate: Date | null,
  toDate: Date | null
): LengthOfServiceDTO | null => {
  if (!fromDate) return null;

  const startDate = new Date(fromDate);
  const endDate = toDate ? new Date(toDate) : new Date();

  if (startDate > endDate) return null;

  let years = endDate.getFullYear() - startDate.getFullYear();
  let months = endDate.getMonth() - startDate.getMonth();
  let days = endDate.getDate() - startDate.getDate();

  if (days < 0) {
    months--;
    const lastDayOfPrevMonth = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      0
    ).getDate();
    days += lastDayOfPrevMonth;
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  if (days >= 30) {
    const additionalMonths = Math.floor(days / 30);
    months += additionalMonths;
    days = days % 30;
  }

  if (months >= 12) {
    const additionalYears = Math.floor(months / 12);
    years += additionalYears;
    months = months % 12;
  }

  return {
    id: "",
    employeeId: "",
    years: years,
    months: months,
    days: days,
  };
};

interface LaborContractPayrollViewProps {
  payrollData: PayrollSummaryDTO;
}

const LaborContractPayrollView = ({
  payrollData,
}: LaborContractPayrollViewProps) => {
  const [selectedTab, setSelectedTab] = useState("current");

  const getProfessionalExperienceInCompany = (
    data: PayrollSummaryDTO | AnnexPayrollSummaryDTO
  ): LengthOfServiceDTO | null => {
    if (
      "professionalЕxperienceInCompany" in data &&
      data.professionalЕxperienceInCompany
    ) {
      return data.professionalЕxperienceInCompany;
    }

    return calculateExperienceFromDates(data.fromDate, data.toDate);
  };

  const tabs = [
    {
      id: "current",
      label: "Current payroll",
      type: "current",
    },
    ...(payrollData?.annexPayrolls && payrollData?.annexPayrolls?.length > 0
      ? payrollData.annexPayrolls
          .sort(
            (a: any, b: any) =>
              new Date(b.fromDate).getTime() - new Date(a.fromDate).getTime()
          )
          .map((annex: AnnexPayrollSummaryDTO) => ({
            id: `annex-${annex.annexPayrollNumber}`,
            label: `${formatDate(annex.contractDate)} / ${translate("AP")} ${
              annex.annexPayrollNumber
            }`,
            type: "annex",
            data: annex,
          }))
      : []),
    {
      id: "main-contract",
      label: `${formatDate(payrollData?.contractDate)} / ${translate("EmpC")} ${
        payrollData?.contractNumber
      }`,
      type: "main",
      data: payrollData,
    },
    {
      id: "additional-terms",
      label: "Additional terms",
      type: "additional-terms",
    },
  ];

  const currentTab = tabs.find((tab) => tab.id === selectedTab);
  const dataToUse = currentTab?.data || payrollData;
  const lastAnnexPayroll = (
    dataToUse as PayrollSummaryDTO
  )?.annexPayrolls?.reduce(
    (max, cur) =>
      Number(cur.annexPayrollNumber ?? 0) > Number(max.annexPayrollNumber ?? 0)
        ? cur
        : max,
    {} as AnnexPayrollSummaryDTO
  );

  const renderCurrent = () => (
    <CustomLayoutContainer>
      <StyledFieldset>
        <Legend>
          {`${translate("EmpC")} ${dataToUse?.contractNumber || ""}`}{" "}
          {lastAnnexPayroll?.annexPayrollNumber &&
            `/ ${translate("AP")} ${lastAnnexPayroll?.annexPayrollNumber}`}
        </Legend>
        <FieldRow
          label="strContractType"
          value={dataToUse?.permanentContractType?.name}
        />
        <FieldRow
          label="Worktime"
          value={`${
            lastAnnexPayroll?.dailyWorktime || dataToUse?.dailyWorktime || ""
          } ${translate("hours")}`}
          includeEmptyRow
        />
        <FieldRow
          label="strDepartment"
          value={
            lastAnnexPayroll?.structureLevelName ||
            dataToUse?.structureLevelName
          }
        />
        <FieldRow
          label="Workplace"
          value={lastAnnexPayroll?.workplace || dataToUse?.workplace}
          includeEmptyRow
        />
        <FieldRow
          label="Reason KT"
          value={
            lastAnnexPayroll?.contractReason?.name ||
            dataToUse?.contractReason?.name
          }
          includeEmptyRow
        />
        <FieldRow
          label="Additional terms"
          value=""
          showThreeDotsIcon={true}
          onThreeDotsClick={() => setSelectedTab("additional-terms")}
        />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Dates</Legend>
        <FieldRow label="Entry" value={formatDate(dataToUse?.fromDate)} />
        <FieldRow
          label="Last change"
          value={formatDate(lastAnnexPayroll?.fromDate || dataToUse.fromDate)}
        />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Experience</Legend>
        <FieldRow
          label="Work experience in the company"
          value={(() => {
            const experience = getProfessionalExperienceInCompany(dataToUse);
            return formatExperience(
              experience?.years,
              experience?.months,
              experience?.days
            );
          })()}
        />
        <FieldRow
          label="Professional experience"
          value={formatExperience(
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.years
              : undefined,
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.months
              : undefined,
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.days
              : undefined
          )}
        />
        <FieldRow
          label="Work experience"
          value={formatExperience(
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.years
              : undefined,
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.months
              : undefined,
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.days
              : undefined
          )}
        />
      </StyledFieldset>
    </CustomLayoutContainer>
  );

  const renderAnnex = () => (
    <ResponsiveGridContainer>
      <StyledFieldset>
        <Legend>{`${translate("AP")} ${
          (dataToUse as AnnexPayrollSummaryDTO)?.annexPayrollNumber || ""
        } - ${translate("Main data")}`}</Legend>
        <FieldRow label="Workplace" value={dataToUse?.workplace} />
        <FieldRow
          label="NKPD position"
          value={dataToUse?.position?.name || ""}
        />
        <FieldRow
          label="Worktime"
          value={`${dataToUse?.dailyWorktime || ""} ${translate("hours")}`}
        />
        <FieldRow label="Reason KT" value={dataToUse?.contractReason?.name} />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Contract Dates and Periods</Legend>
        <FieldRow
          label="Contract date"
          value={formatDate(dataToUse.contractDate)}
        />
        <FieldRow
          label="Effective from"
          value={formatDate(dataToUse?.fromDate)}
        />
        <FieldRow
          label="Validity period"
          value={formatDate(dataToUse?.contractTermDate)}
        />
        <FieldRow label="Date to" value={formatDate(dataToUse?.toDate)} />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Codes</Legend>
        <FieldRow label="KID code" value={dataToUse?.kid} />
        <FieldRow label="EKATTE code" value={dataToUse?.ekatte} />
        <FieldRow label="NKPD code" value={dataToUse?.nkpd} />
      </StyledFieldset>
    </ResponsiveGridContainer>
  );

  const renderMain = () => (
    <ResponsiveGridContainer>
      <StyledFieldset>
        <Legend>{`${translate("EmpC")} ${
          dataToUse?.contractNumber || ""
        } - ${translate("Main data")}`}</Legend>
        <FieldRow label="Workplace" value={dataToUse?.workplace} />

        <FieldRow label="NKPD position" value={dataToUse?.position?.name} />
        <FieldRow
          label="Worktime"
          value={`${dataToUse?.dailyWorktime || ""} ${translate("hours")}`}
        />
        <FieldRow
          label="Contract type"
          value={dataToUse?.permanentContractType?.name}
        />
        <FieldRow
          label="Contract term date"
          value={
            dataToUse?.contractTermDate
              ? new Date(dataToUse.contractTermDate).toLocaleDateString("bg-BG")
              : ""
          }
        />
        <FieldRow label="Reason KT" value={dataToUse?.contractReason?.name} />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Contract Dates and Periods</Legend>
        <FieldRow
          label="Contract date"
          value={formatDate(dataToUse?.contractDate)}
        />
        <FieldRow
          label="First working day"
          value={formatDate(dataToUse?.fromDate)}
        />
        <FieldRow label="Probationary period" value=" " />
        <FieldRow label="Notice period" value=" " />
        <FieldRow
          label="Last working day"
          value={formatDate(dataToUse?.toDate)}
        />
        <FieldRow
          label="Termination date"
          value={formatDate(dataToUse?.contractTerminationDate)}
        />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Codes</Legend>
        <FieldRow label="KID code" value={dataToUse?.kid} />
        <FieldRow label="EKATTE code" value={dataToUse?.ekatte} />
        <FieldRow label="NKPD code" value={dataToUse?.nkpd} />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Experience</Legend>
        <FieldRow
          label="Work experience"
          value={formatExperience(
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.years
              : undefined,
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.months
              : undefined,
            "workExperience" in dataToUse
              ? dataToUse?.workExperience?.days
              : undefined
          )}
        />
        <FieldRow
          label="Professional experience"
          value={formatExperience(
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.years
              : undefined,
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.months
              : undefined,
            "professionalЕxperience" in dataToUse
              ? dataToUse?.professionalЕxperience?.days
              : undefined
          )}
        />
      </StyledFieldset>
    </ResponsiveGridContainer>
  );

  const renderAdditionalDetails = () => (
    <AdditionalTerms data={dataToUse} tabType="additional-terms" />
  );

  const renderContent = () => {
    switch (currentTab?.type) {
      case "current":
        return renderCurrent();
      case "annex":
        return renderAnnex();
      case "main":
        return renderMain();
      case "additional-terms":
        return renderAdditionalDetails();
      default:
        return renderCurrent();
    }
  };

  return (
    <WrapperContainer>
      {renderContent()}
      <RightColumn>
        {tabs.map((tab) => (
          <MenuItem key={tab.id} onClick={() => setSelectedTab(tab.id)}>
            <MenuText $isSelected={selectedTab === tab.id}>
              {tab.label}
            </MenuText>
            <MenuLine />
          </MenuItem>
        ))}
      </RightColumn>
    </WrapperContainer>
  );
};

export default LaborContractPayrollView;
