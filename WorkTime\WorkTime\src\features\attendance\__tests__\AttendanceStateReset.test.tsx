import { render, waitFor, cleanup, act } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import Attendance from "../Attendance";
import { useCompany } from "../../companies/CompanyContext";
import { useUserEmployee } from "../../UserEmployeeContext";
import { Employee } from "../useFilteredEmployees";
import { LightPayrollDTO } from "../../../models/DTOs/payrolls/LightPayrollDTO";

// Mock the child components with state tracking
let mockDatesTableContainerProps: any = {};
let mockAttendancesRightViewProps: any = {};

jest.mock("../DatesTableContainer", () => {
  return function MockDatesTableContainer(props: any) {
    mockDatesTableContainerProps = props;
    return <div data-testid="dates-table-container" />;
  };
});

jest.mock("../AttendancesRightView", () => {
  return function MockAttendancesRightView(props: any) {
    mockAttendancesRightViewProps = props;
    return <div data-testid="attendances-right-view" />;
  };
});

// Mock Redux hooks
jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: () => ({ holidays: [] }),
}));

// Mock holiday actions
jest.mock("../../holidays/holidayActions", () => ({
  onHolidaysLoaded: jest.fn(),
  selectHolidays: () => ({ holidays: [] }),
}));

// Mock user employee context
const mockUserEmployee = {
  payrolls: [
    {
      id: "payroll-1",
      workTimeId: "worktime-1",
      employee: {
        userId: "user-1",
        workTimeId: "emp-1",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        egn: "1234567890",
      },
      structureLevelId: "lvl-1",
      leaves: [],
      annexPayrolls: [],
    },
  ],
  permissions: ["Attendances.Write"],
};

jest.mock("../../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    userEmployee: mockUserEmployee,
  }),
}));

// Mock company context with ability to change company
let mockCompany = { id: "company-1", name: "Company 1" };
const mockSetCompany = jest.fn();
const mockResetCompany = jest.fn();

jest.mock("../../companies/CompanyContext", () => ({
  useCompany: () => ({
    company: mockCompany,
    setCompany: mockSetCompany,
    resetCompany: mockResetCompany,
  }),
}));

// Mock search params
const mockSetSearchParams = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useSearchParams: () => [new URLSearchParams(""), mockSetSearchParams],
}));

describe("Attendance State Reset on Company Change", () => {
  beforeEach(() => {
    cleanup();
    jest.clearAllMocks();
    mockCompany = { id: "company-1", name: "Company 1" };
    mockDatesTableContainerProps = {};
    mockAttendancesRightViewProps = {};
  });

  const renderAttendance = (companyId: string = "company-1") => {
    return render(
      <MemoryRouter initialEntries={[`/${companyId}/attendance`]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );
  };

  test("resets selectedEmployee to undefined when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify initial state
    expect(mockAttendancesRightViewProps.selectedEmployee).toBeUndefined();

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify state is reset
    expect(mockAttendancesRightViewProps.selectedEmployee).toBeUndefined();
  });

  test("resets selectedPayroll to undefined when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify initial state - component sets default payroll from userEmployee
    expect(mockAttendancesRightViewProps.selectedPayroll).toBeDefined();

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify state is reset - payroll should be undefined after company change
    expect(mockAttendancesRightViewProps.selectedPayroll).toBeUndefined();
  });

  test("resets hoveredEmployee to undefined when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify initial state
    expect(mockAttendancesRightViewProps.hoveredEmployee).toBeUndefined();

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify state is reset
    expect(mockAttendancesRightViewProps.hoveredEmployee).toBeUndefined();
  });

  test("resets showMyAbsences to false when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify initial state
    expect(mockAttendancesRightViewProps.showMyAbsences).toBe(false);

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify state is reset
    expect(mockAttendancesRightViewProps.showMyAbsences).toBe(false);
  });

  test("passes correct props to child components after company change", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Verify all props are passed correctly
    expect(mockDatesTableContainerProps).toHaveProperty("selectedPayroll");
    expect(mockDatesTableContainerProps).toHaveProperty("setSelectedPayroll");
    expect(mockDatesTableContainerProps).toHaveProperty("selectedEmployee");
    expect(mockDatesTableContainerProps).toHaveProperty("hoveredEmployee");
    expect(mockDatesTableContainerProps).toHaveProperty("showMyAbsences");
    expect(mockDatesTableContainerProps).toHaveProperty("selectedMonth");
    expect(mockDatesTableContainerProps).toHaveProperty("selectedYear");
    expect(mockDatesTableContainerProps).toHaveProperty("setSelectedMonth");
    expect(mockDatesTableContainerProps).toHaveProperty("setSelectedYear");
    expect(mockDatesTableContainerProps).toHaveProperty("holidays");

    expect(mockAttendancesRightViewProps).toHaveProperty("selectedPayroll");
    expect(mockAttendancesRightViewProps).toHaveProperty("selectedEmployee");
    expect(mockAttendancesRightViewProps).toHaveProperty("selectedYear");
    expect(mockAttendancesRightViewProps).toHaveProperty("selectedMonth");
    expect(mockAttendancesRightViewProps).toHaveProperty("onSelectEmployee");
    expect(mockAttendancesRightViewProps).toHaveProperty("hoveredEmployee");
    expect(mockAttendancesRightViewProps).toHaveProperty("onEmployeeHover");
    expect(mockAttendancesRightViewProps).toHaveProperty("showMyAbsences");
    expect(mockAttendancesRightViewProps).toHaveProperty("onToggleMyAbsences");
    expect(mockAttendancesRightViewProps).toHaveProperty("holidays");
  });
});
