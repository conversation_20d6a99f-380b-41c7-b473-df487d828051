﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class DeclineEmployeePropertyEditHandler : IRequestHandler<DeclineEmployeePropertyEditRequest, IResult>
    {
        private readonly IEmployeesService _employeesService;
        private readonly GlobalEmployee _globalEmployee;
        private readonly GlobalUser _globalUser;
        private readonly IMediator _mediator;

        public DeclineEmployeePropertyEditHandler(IEmployeesService employeesService, GlobalEmployee globalEmployee, IMediator mediator, GlobalUser globalUser)
        {
            _employeesService = employeesService;
            _globalEmployee = globalEmployee;
            _mediator = mediator;
            _globalUser = globalUser;
        }

        public async Task<IResult> Handle(DeclineEmployeePropertyEditRequest request, CancellationToken cancellationToken)
        {
            if (!_globalEmployee.Permissions.Contains(DefaultPermissions.Employees.Write))
                return Results.Unauthorized();

            await _employeesService.DeclineEmployeePropertyEditAsync(request);
            if (request.EmployeeId.HasValue)
            {
                var employee = await _employeesService.GetEmployeeAsync(request.EmployeeId.Value);

                var notificationConfirm = new EditEmployeeDeclineNotification(
                    employee,
                    request.PayrollId,
                    employee.CompanyId,
                    _globalUser.Id,
                    $"{_globalUser.FirstName} {_globalUser.LastName}",
                    request.TabName);
                await _mediator.Publish(notificationConfirm, cancellationToken);
            }

            return Results.Ok();
        }
    }
}
