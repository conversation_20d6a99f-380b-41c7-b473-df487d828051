import { useEffect, useState } from "react";
import styled from "styled-components";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import profileWoman from "../../../assets/images/profile/WomanProfilePic.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";
import {
  onProfileLoaded,
  selectEmployees,
} from "../../employees/employeesActions";

import copyIconHover from "../../../assets/images/profile/copyIconHover.svg";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import ApprovedUserEditIcon from "../../../components/ApproveEdit/ApprovedUserEditIcon";
import ApproveEditCardHoverIcon from "../../../components/ApproveEdit/ApproveEditCardHoverIcon";
import PendingUserEditIcon from "../../../components/ApproveEdit/PendingUserEditIcon";
import ViewEditCardHoverIcon from "../../../components/ApproveEdit/ViewEditCardHoverIcon";
import { Genders } from "../../../constants/enum";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import {
  BankAccountDTO,
  BankAccountPurpose,
} from "../../../models/DTOs/bankAccount/BankAccountDTO";
import { ApproveEmployeePropertyEditDTO } from "../../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeePropertyEditDTO } from "../../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import {
  EditSource,
  EditStatus,
  EmployeePropertyEditDTO,
} from "../../../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import {
  approveEmployeePropertyEdit,
  declineEmployeePropertyEdit,
  deleteEmployeePropertyEdit,
} from "../../../services/employees/employeesService";
import { PropertyEditService } from "../../../services/employees/propertyEditService";
import Translator, { translate } from "../../../services/language/Translator";
import { useDefaultPlaces } from "../../DefaultLocationDataContext";
import { useMenu } from "../../MenuContext";
import { selectStructureLevels } from "../../company-structure/companyStructureActions";
import { StructureLevelDTO } from "../../../models/DTOs/companyStructure/StructureLevelDTO";

const FieldsetRow = styled(Container)`
  display: grid;
  grid-template-columns: 17% 25% 13% 30%;
  gap: 2rem;
  margin: 0.5rem 1rem;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 1rem;
`;

const EmployeeName = styled(Label)`
  text-align: center;
  font-weight: bold;
  font-size: 1.4rem;
  word-wrap: normal;
  margin-top: 0.5rem;
`;

const DepartmentInfo = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 2rem;
`;

const DepartmentName = styled(Label)`
  font-size: 1.15rem;
  margin-top: 0.2rem;
  color: var(--profile-department-name-font-color);
`;

const DepartmentLeader = styled(Label)`
  font-size: 1.05rem;
  color: var(--profile-department-leader-name-font-color);
  margin-top: 0.1rem;
`;

const ContractInfo = styled(Container)`
  display: flex;
  text-align: center;
  position: relative;
  top: 4rem;
  flex-direction: column;
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 12rem;
  padding: 1rem;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 9rem;
    margin-left: -2.5rem;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1300px) {
    flex-direction: column;
    margin-left: 0;
    width: 100%;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 1rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
  }

  @media (max-width: 1000px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
`;
const EditButton = styled(Button)<{
  $normalImage: string;
  $hoverImage: string;
  $isDisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.$normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.$hoverImage});
  }
`;

const ButtonsContainer = styled(Container)`
  display: flex;
`;

const EditNameButton = styled(Button)<{
  $normalImage: string;
  $hoverImage: string;
  $isDisabled?: boolean;
}>`
  width: 2.5rem;
  height: 2.5rem;
  background: no-repeat center / 1.8rem url(${(p) => p.$normalImage});
  cursor: pointer;
  padding: 0;
  background-color: transparent;

  &:hover {
    background: no-repeat center / 1.8rem url(${(p) => p.$hoverImage});
  }
`;

interface Props {
  employeeName: string;
  shouldShowAdminEditButtons: boolean;
  shouldShowUserEditButtons: boolean;
}

const PersonalData = ({
  employeeName,
  shouldShowAdminEditButtons,
  shouldShowUserEditButtons,
}: Props) => {
  const dispatch = useAppDispatch();

  const employeesState = useAppSelector(selectEmployees) as any;
  const profile = employeesState.currentProfile as PersonalInformationDTO;

  const setProfile = (profile: PersonalInformationDTO | null) => {
    dispatch(onProfileLoaded(profile));
  };
  const [identityCardAddress, setIdentityCardAddress] =
    useState<AddressDTO | null>();
  const { toggleMenu, changeView } = useMenu();
  const { cities, districts, municipalities } = useDefaultPlaces();

  function formatFullAddress(address?: AddressDTO | null) {
    if (!address) return "";
    const parts = [];
    if (address.neighborhood && address.neighborhood !== "")
      parts.push(address.neighborhood);
    if (address.street && address.street !== "") parts.push(address.street);
    if (address.block && address.block !== "") parts.push(address.block);
    if (address.apartment && address.apartment !== "")
      parts.push(address.apartment);
    return parts.join(", ");
  }

  function formatAddressFields(
    neighborhood?: string | null,
    street?: string | null,
    block?: string | null,
    apartment?: string | null
  ): string {
    if (!neighborhood && !street && !block && !apartment) return "";
    const parts = [];
    if (neighborhood && neighborhood !== "") parts.push(neighborhood);
    if (street && street !== "") parts.push(street);
    if (block && block !== "") parts.push(block);
    if (apartment && apartment !== "") parts.push(apartment);

    return parts.join(", ");
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, ".");
  };

  const handleEdit = (step: number) => {
    changeView("edit-employee", "other", {
      incomingProfile: profile,
      step,
      propertyEdits: profile.employeePropertyEdits || [],
      shouldShowAdminEditButtons,
      shouldShowUserEditButtons,
      payrollId: profile.payrollPersonalData?.id,
    });
    toggleMenu();
  };

  const handleCopyPersonName = async () => {
    try {
      await navigator.clipboard.writeText(employeeName);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  const hasPendingPropertyEdit = (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ): boolean => {
    return PropertyEditService.hasPendingEditForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );
  };

  const hasPropertyEdit = (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ): boolean => {
    return PropertyEditService.hasEditForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );
  };

  const getOldValue = (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ): any => {
    const oldValue = PropertyEditService.getOldValueForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );

    try {
      return oldValue ? JSON.parse(oldValue) : oldValue;
    } catch {
      return oldValue;
    }
  };

  const getPropertyEditInfo = (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ) => {
    const edit = PropertyEditService.getEditForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );
    return edit;
  };

  const handleDelete = async (editInfoId: string) => {
    try {
      await deleteEmployeePropertyEdit(editInfoId);
      const updatedEdits = (profile.employeePropertyEdits || []).filter(
        (edit: EmployeePropertyEditDTO) => edit.id !== editInfoId
      );
      setProfile({
        ...profile,
        employeePropertyEdits: updatedEdits,
      });
    } catch (error) {
      console.error("Failed to delete property edit:", error);
    }
  };

  const renderUserEditIcon = (propertyName: string, previousValue?: string) => {
    const editInfo = getPropertyEditInfo(propertyName);
    if (!editInfo) return null;

    const handleDelete = async () => {
      try {
        await deleteEmployeePropertyEdit(editInfo.id);
        const updatedEdits = (profile.employeePropertyEdits || []).filter(
          (edit: EmployeePropertyEditDTO) => edit.id !== editInfo.id
        );
        setProfile({
          ...profile,
          employeePropertyEdits: updatedEdits,
        });
      } catch (error) {
        console.error("Failed to delete property edit:", error);
      }
    };

    if (editInfo.editSource === EditSource.AdminEdit) {
      return (
        <ViewEditCardHoverIcon
          previousValue={previousValue}
          onDelete={handleDelete}
        />
      );
    } else if (editInfo.editSource === EditSource.UserEdit) {
      if (editInfo.editStatus === EditStatus.Pending) {
        return <PendingUserEditIcon />;
      } else if (editInfo.editStatus === EditStatus.Approved) {
        return <ApprovedUserEditIcon onDelete={handleDelete} />;
      }
    }
    return null;
  };

  const renderSubObjectUserEditIcon = (
    objectName: string,
    objectId: string,
    propertyName: string,
    previousValue?: string
  ) => {
    const editInfo = getPropertyEditInfo(propertyName, objectName, objectId);
    if (!editInfo) return null;

    const handleDelete = async () => {
      try {
        await deleteEmployeePropertyEdit(editInfo.id);
        const updatedEdits = (profile.employeePropertyEdits || []).filter(
          (edit: EmployeePropertyEditDTO) => edit.id !== editInfo.id
        );
        setProfile({
          ...profile,
          employeePropertyEdits: updatedEdits,
        });
      } catch (error) {
        console.error("Failed to delete property edit:", error);
      }
    };

    if (editInfo.editSource === EditSource.AdminEdit) {
      return (
        <ViewEditCardHoverIcon
          previousValue={previousValue}
          onDelete={handleDelete}
        />
      );
    } else if (editInfo.editSource === EditSource.UserEdit) {
      if (editInfo.editStatus === EditStatus.Pending) {
        return <PendingUserEditIcon />;
      } else if (editInfo.editStatus === EditStatus.Approved) {
        return <ApprovedUserEditIcon onDelete={handleDelete} />;
      }
    }
    return null;
  };

  const handleApprove = async (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ) => {
    const pendingEdit = PropertyEditService.getPendingEditForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );

    if (pendingEdit) {
      await approveEmployeePropertyEdit(
        new ApproveEmployeePropertyEditDTO({
          propertyEditId: pendingEdit.id,
          employeeId: profile.employee.workTimeId,
          payrollId: profile.payrollPersonalData?.id,
          tabName: translate("Personal Data"),
        })
      );

      const updatedEdits = (profile.employeePropertyEdits || []).filter(
        (edit: EmployeePropertyEditDTO) => edit.id !== pendingEdit.id
      );

      setProfile({
        ...profile,
        employeePropertyEdits: updatedEdits,
      });
    }
  };

  const handleDecline = async (
    propertyName: string,
    objectName: string = "Employee",
    objectId: string = profile.employee.workTimeId
  ) => {
    const pendingEdit = PropertyEditService.getPendingEditForProperty(
      profile.employeePropertyEdits || [],
      objectName,
      objectId,
      propertyName
    );

    if (pendingEdit) {
      await declineEmployeePropertyEdit(
        new DeclineEmployeePropertyEditDTO({
          propertyEditId: pendingEdit.id,
          employeeId: profile.employee.workTimeId,
          payrollId: profile.payrollPersonalData?.id,
          tabName: translate("Personal Data"),
        })
      );

      const updatedEdits = (profile.employeePropertyEdits || []).filter(
        (edit: EmployeePropertyEditDTO) => edit.id !== pendingEdit.id
      );

      let revertValue = pendingEdit.oldValue;
      try {
        revertValue = revertValue ? JSON.parse(revertValue) : revertValue;
      } catch {}

      let updatedProfile = { ...profile };

      if (objectName === "Employee") {
        const mappedPropertyName =
          PropertyEditService.mapPropertyName(propertyName);
        const revertedEmployee = {
          ...profile.employee,
          [mappedPropertyName]: revertValue,
        };

        updatedProfile = {
          ...profile,
          employee: revertedEmployee,
          employeePropertyEdits: updatedEdits,
        };
      } else if (objectName === "BankAccount") {
        const mappedPropertyName =
          PropertyEditService.mapPropertyName(propertyName);
        const updatedBankAccounts = profile.employee.bankAccounts?.map(
          (account: BankAccountDTO) => {
            if (account.id === objectId) {
              return {
                ...account,
                [mappedPropertyName]: revertValue,
              };
            }
            return account;
          }
        );

        updatedProfile = {
          ...profile,
          employee: {
            ...profile.employee,
            bankAccounts: updatedBankAccounts,
          },
          employeePropertyEdits: updatedEdits,
        };
      } else if (objectName === "Address") {
        const mappedPropertyName =
          PropertyEditService.mapPropertyName(propertyName);
        const updatedAddresses = profile.addresses?.map(
          (address: AddressDTO) => {
            if (address.id === objectId) {
              return {
                ...address,
                [mappedPropertyName]: revertValue,
              };
            }
            return address;
          }
        );

        updatedProfile = {
          ...profile,
          addresses: updatedAddresses,
          employeePropertyEdits: updatedEdits,
        };
      } else {
        updatedProfile = {
          ...profile,
          employeePropertyEdits: updatedEdits,
        };
      }

      setProfile(updatedProfile);
    }
  };

  useEffect(() => {
    setIdentityCardAddress(
      profile?.addresses?.find(
        (address: AddressDTO) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.IdentityCard
      ) || null
    );
  }, [profile]);

  return (
    <WrapperContainer>
      <LeftContainer data-testid="profile-left-container">
        <EmployeeImage
          src={
            profile?.employee?.gender === Genders.Male
              ? profileMan
              : profileWoman
          }
          alt="Employee's profile picture"
          data-testid="profile-employee-image"
        />
        <EmployeeName data-testid="profile-employee-name">
          {shouldShowAdminEditButtons &&
          (hasPendingPropertyEdit("FirstName") ||
            hasPendingPropertyEdit("SecondName") ||
            hasPendingPropertyEdit("LastName"))
            ? `${
                hasPendingPropertyEdit("FirstName")
                  ? getOldValue("FirstName") || ""
                  : profile.employee.firstName || ""
              } ${
                hasPendingPropertyEdit("SecondName")
                  ? getOldValue("SecondName") || ""
                  : profile.employee.secondName || ""
              } ${
                hasPendingPropertyEdit("LastName")
                  ? getOldValue("LastName") || ""
                  : profile.employee.lastName || ""
              }`.trim()
            : employeeName}
        </EmployeeName>
        {shouldShowAdminEditButtons &&
        (hasPendingPropertyEdit("FirstName") ||
          hasPendingPropertyEdit("SecondName") ||
          hasPendingPropertyEdit("LastName")) ? (
          <ApproveEditCardHoverIcon
            newValue={employeeName}
            onConfirm={async () => {
              if (hasPendingPropertyEdit("FirstName"))
                await handleApprove("FirstName");
              if (hasPendingPropertyEdit("SecondName"))
                await handleApprove("SecondName");
              if (hasPendingPropertyEdit("LastName"))
                await handleApprove("LastName");
            }}
            onCancel={async () => {
              if (hasPendingPropertyEdit("FirstName"))
                await handleDecline("FirstName");
              if (hasPendingPropertyEdit("SecondName"))
                await handleDecline("SecondName");
              if (hasPendingPropertyEdit("LastName"))
                await handleDecline("LastName");
            }}
          />
        ) : shouldShowUserEditButtons &&
          (hasPropertyEdit("FirstName", "Employee") ||
            hasPropertyEdit("SecondName", "Employee") ||
            hasPropertyEdit("LastName", "Employee")) ? (
          (() => {
            const firstNameEdit = getPropertyEditInfo("FirstName");
            const secondNameEdit = getPropertyEditInfo("SecondName");
            const lastNameEdit = getPropertyEditInfo("LastName");

            const hasAnyNameEdit =
              firstNameEdit || secondNameEdit || lastNameEdit;
            if (!hasAnyNameEdit) return null;

            const editInfo = firstNameEdit || secondNameEdit || lastNameEdit;

            if (editInfo?.editSource === EditSource.AdminEdit) {
              return (
                <ViewEditCardHoverIcon
                  previousValue={`${
                    getOldValue("FirstName") || profile.employee.firstName
                  } ${
                    getOldValue("SecondName") ||
                    profile.employee.secondName ||
                    ""
                  } ${
                    getOldValue("LastName") || profile.employee.lastName
                  }`.trim()}
                />
              );
            } else if (editInfo?.editSource === EditSource.UserEdit) {
              if (editInfo.editStatus === EditStatus.Pending) {
                return <PendingUserEditIcon />;
              } else if (editInfo.editStatus === EditStatus.Approved) {
                return (
                  <ApprovedUserEditIcon
                    onDelete={() => {
                      if (firstNameEdit) handleDelete(firstNameEdit.id);
                      if (secondNameEdit) handleDelete(secondNameEdit.id);
                      if (lastNameEdit) handleDelete(lastNameEdit.id);
                    }}
                  />
                );
              }
            }
            return null;
          })()
        ) : null}
        <ButtonsContainer>
          <EditNameButton
            data-testid="copy-name-button"
            $normalImage={copyIcon}
            $hoverImage={copyIconHover}
            onClick={handleCopyPersonName}
            label=""
            $isDisabled={true}
          />
          <EditNameButton
            data-testid="edit-company-button"
            $normalImage={editIcon}
            $hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            $isDisabled={true}
          />
        </ButtonsContainer>
        <DepartmentInfo data-testid="profile-department-info">
          <DepartmentName data-testid="profile-department-name">
            {profile?.payrollPersonalData?.structureLevel}
          </DepartmentName>
          <DepartmentLeader data-testid="profile-department-leader">
            {profile?.payrollPersonalData?.position}
          </DepartmentLeader>
        </DepartmentInfo>
        <ContractInfo data-testid="profile-contract-info"></ContractInfo>
      </LeftContainer>
      <RightContainer data-testid="profile-right-container">
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="personal-information-fieldset"
        >
          <EditButton
            data-testid="edit-company-button"
            $normalImage={editIcon}
            $hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            $isDisabled={true}
          />
          <Legend data-testid="information-legend">Personal Information</Legend>
          <FieldsetRow data-testid="first-row">
            <LabelColumn>
              <LightLabel>EGN / LNCH</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons && hasPendingPropertyEdit("EGN")
                  ? getOldValue("EGN")
                  : profile?.employee?.egn}
              </ValueLabel>
              {shouldShowAdminEditButtons && hasPendingPropertyEdit("EGN") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.egn}
                  onConfirm={async () => {
                    await handleApprove("EGN");
                  }}
                  onCancel={async () => {
                    await handleDecline("EGN");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("EGN", "Employee") ? (
                renderUserEditIcon("EGN", getOldValue("EGN"))
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>IBAN</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const salaryBankAccount =
                    profile?.employee?.bankAccounts?.find(
                      (account: BankAccountDTO) =>
                        account.purpose.identifier === BankAccountPurpose.Salary
                    );
                  const bankAccountId = salaryBankAccount?.id;
                  if (
                    shouldShowAdminEditButtons &&
                    bankAccountId &&
                    hasPendingPropertyEdit("Iban", "BankAccount", bankAccountId)
                  ) {
                    return getOldValue("Iban", "BankAccount", bankAccountId);
                  }
                  return profile?.iban;
                })()}
              </ValueLabel>
              {(() => {
                const salaryBankAccount = profile?.employee?.bankAccounts?.find(
                  (account: BankAccountDTO) =>
                    account.purpose.identifier === BankAccountPurpose.Salary
                );
                const bankAccountId = salaryBankAccount?.id;
                return (
                  bankAccountId &&
                  hasPendingPropertyEdit("Iban", "BankAccount", bankAccountId)
                );
              })() && shouldShowAdminEditButtons ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.iban}
                  onConfirm={async () => {
                    const salaryBankAccount =
                      profile?.employee?.bankAccounts?.find(
                        (account: BankAccountDTO) =>
                          account.purpose.identifier ===
                          BankAccountPurpose.Salary
                      );
                    if (salaryBankAccount?.id) {
                      await handleApprove(
                        "Iban",
                        "BankAccount",
                        salaryBankAccount.id
                      );
                    }
                  }}
                  onCancel={async () => {
                    const salaryBankAccount =
                      profile?.employee?.bankAccounts?.find(
                        (account: BankAccountDTO) =>
                          account.purpose.identifier ===
                          BankAccountPurpose.Salary
                      );
                    if (salaryBankAccount?.id) {
                      await handleDecline(
                        "Iban",
                        "BankAccount",
                        salaryBankAccount.id
                      );
                    }
                  }}
                />
              ) : (() => {
                  const salaryBankAccount =
                    profile?.employee?.bankAccounts?.find(
                      (account: BankAccountDTO) =>
                        account.purpose.identifier === BankAccountPurpose.Salary
                    );
                  const bankAccountId = salaryBankAccount?.id;
                  return (
                    bankAccountId &&
                    hasPropertyEdit("Iban", "BankAccount", bankAccountId)
                  );
                })() && shouldShowUserEditButtons ? (
                (() => {
                  const salaryBankAccount =
                    profile?.employee?.bankAccounts?.find(
                      (account: BankAccountDTO) =>
                        account.purpose.identifier === BankAccountPurpose.Salary
                    );
                  const bankAccountId = salaryBankAccount?.id;
                  if (!bankAccountId) return null;
                  return renderSubObjectUserEditIcon(
                    "BankAccount",
                    bankAccountId,
                    "Iban",
                    getOldValue("Iban", "BankAccount", bankAccountId) || ""
                  );
                })()
              ) : null}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="second-row">
            <LabelColumn>
              <LightLabel>Date of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("BirthDate")
                  ? formatDate(getOldValue("BirthDate"))
                  : formatDate(profile?.employee?.birthDate)}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("BirthDate") ? (
                <ApproveEditCardHoverIcon
                  newValue={formatDate(profile?.employee?.birthDate)}
                  onConfirm={async () => {
                    await handleApprove("BirthDate");
                  }}
                  onCancel={async () => {
                    await handleDecline("BirthDate");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("BirthDate", "Employee") ? (
                renderUserEditIcon(
                  "BirthDate",
                  formatDate(formatDate(getOldValue("BirthDate")))
                )
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>
                {profile?.email || profile?.email !== ""
                  ? "E-mail"
                  : "User number"}
              </LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.email || profile?.email !== ""
                  ? profile?.email
                  : profile?.code}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="third-row">
            <LabelColumn>
              <LightLabel>Place of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("BirthPlace")
                  ? getOldValue("BirthPlace")
                  : profile?.employee?.birthPlace}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("BirthPlace") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.birthPlace}
                  onConfirm={async () => {
                    await handleApprove("BirthPlace");
                  }}
                  onCancel={async () => {
                    await handleDecline("BirthPlace");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("BirthPlace", "Employee") ? (
                renderUserEditIcon("BirthPlace", getOldValue("BirthPlace"))
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Phone number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons && hasPendingPropertyEdit("Phone")
                  ? getOldValue("Phone")
                  : profile?.employee?.phone}
              </ValueLabel>
              {shouldShowAdminEditButtons && hasPendingPropertyEdit("Phone") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.phone}
                  onConfirm={async () => {
                    await handleApprove("Phone");
                  }}
                  onCancel={async () => {
                    await handleDecline("Phone");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("Phone", "Employee") ? (
                renderUserEditIcon("Phone", getOldValue("Phone"))
              ) : null}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="address-fieldset"
        >
          <Legend data-testid="address-legend">ID card details</Legend>
          <FieldsetRow data-testid="address-row">
            <EditButton
              data-testid="edit-company-button"
              $normalImage={editIcon}
              $hoverImage={editIconHover}
              onClick={() => handleEdit(2)}
              label=""
              $isDisabled={true}
            />
            <LabelColumn>
              <LightLabel>Number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("IDNumber")
                  ? getOldValue("IDNumber")
                  : profile?.employee?.idNumber}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("IDNumber") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.idNumber}
                  onConfirm={async () => {
                    await handleApprove("IDNumber");
                  }}
                  onCancel={async () => {
                    await handleDecline("IDNumber");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("IDNumber", "Employee") ? (
                renderUserEditIcon("IDNumber", getOldValue("IDNumber"))
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>City</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const addressId = identityCardAddress?.id;

                  if (
                    shouldShowAdminEditButtons &&
                    addressId &&
                    (hasPendingPropertyEdit("CityName", "Address", addressId) ||
                      hasPendingPropertyEdit("CityId", "Address", addressId))
                  ) {
                    return (
                      getOldValue("CityName", "Address", addressId) ||
                      cities.find(
                        (c) =>
                          c.id === getOldValue("CityId", "Address", addressId)
                      )?.name
                    );
                  }
                  return (
                    identityCardAddress?.city?.name ??
                    identityCardAddress?.cityName
                  );
                })()}
              </ValueLabel>
              {(() => {
                const addressId = identityCardAddress?.id;
                return (
                  addressId &&
                  (hasPendingPropertyEdit("CityName", "Address", addressId) ||
                    hasPendingPropertyEdit("CityId", "Address", addressId))
                );
              })() && shouldShowAdminEditButtons ? (
                <ApproveEditCardHoverIcon
                  newValue={
                    identityCardAddress?.city?.name ??
                    identityCardAddress?.cityName
                  }
                  onConfirm={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit("CityName", "Address", addressId)
                      ) {
                        await handleApprove("CityName", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit("CityId", "Address", addressId)
                      ) {
                        await handleApprove("CityId", "Address", addressId);
                      }
                    }
                  }}
                  onCancel={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit("CityName", "Address", addressId)
                      ) {
                        await handleDecline("CityName", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit("CityId", "Address", addressId)
                      ) {
                        await handleDecline("CityId", "Address", addressId);
                      }
                    }
                  }}
                />
              ) : shouldShowUserEditButtons &&
                (() => {
                  const addressId = identityCardAddress?.id;
                  return (
                    addressId &&
                    (hasPropertyEdit("CityName", "Address", addressId) ||
                      hasPropertyEdit("CityId", "Address", addressId))
                  );
                })() ? (
                (() => {
                  const addressId = identityCardAddress?.id;
                  if (!addressId) return null;

                  const cityNameEdit = getPropertyEditInfo(
                    "CityName",
                    "Address",
                    addressId
                  );
                  const cityEdit = getPropertyEditInfo(
                    "CityId",
                    "Address",
                    addressId
                  );
                  const editInfo = cityNameEdit || cityEdit;

                  if (!editInfo) return null;

                  const previousValue =
                    getOldValue("CityName", "Address", addressId) ||
                    cities.find(
                      (c) =>
                        c.id === getOldValue("CityId", "Address", addressId)
                    )?.name ||
                    "";

                  return renderSubObjectUserEditIcon(
                    "Address",
                    addressId,
                    editInfo.propertyName,
                    previousValue
                  );
                })()
              ) : null}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued on</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("IDIssueDate")
                  ? formatDate(getOldValue("IDIssueDate"))
                  : formatDate(profile?.employee?.idIssueDate)}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("IDIssueDate") ? (
                <ApproveEditCardHoverIcon
                  newValue={formatDate(profile?.employee?.idIssueDate)}
                  onConfirm={async () => {
                    await handleApprove("IDIssueDate");
                  }}
                  onCancel={async () => {
                    await handleDecline("IDIssueDate");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("IDIssueDate", "Employee") ? (
                renderUserEditIcon(
                  "IDIssueDate",
                  formatDate(getOldValue("IDIssueDate"))
                )
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>District</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const addressId = identityCardAddress?.id;
                  if (
                    shouldShowAdminEditButtons &&
                    addressId &&
                    (hasPendingPropertyEdit(
                      "DistrictName",
                      "Address",
                      addressId
                    ) ||
                      hasPendingPropertyEdit(
                        "DistrictId",
                        "Address",
                        addressId
                      ))
                  ) {
                    return (
                      getOldValue("DistrictName", "Address", addressId) ||
                      districts.find(
                        (d) =>
                          d.id ===
                          getOldValue("DistrictId", "Address", addressId)
                      )?.name
                    );
                  }
                  return (
                    identityCardAddress?.district?.name ??
                    identityCardAddress?.districtName
                  );
                })()}
              </ValueLabel>
              {(() => {
                const addressId = identityCardAddress?.id;
                return (
                  addressId &&
                  (hasPendingPropertyEdit(
                    "DistrictName",
                    "Address",
                    addressId
                  ) ||
                    hasPendingPropertyEdit("DistrictId", "Address", addressId))
                );
              })() && shouldShowAdminEditButtons ? (
                <ApproveEditCardHoverIcon
                  newValue={
                    identityCardAddress?.district?.name ??
                    identityCardAddress?.districtName
                  }
                  onConfirm={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit(
                          "DistrictName",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove(
                          "DistrictName",
                          "Address",
                          addressId
                        );
                      }
                      if (
                        hasPendingPropertyEdit(
                          "DistrictId",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove("DistrictId", "Address", addressId);
                      }
                    }
                  }}
                  onCancel={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit(
                          "DistrictName",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline(
                          "DistrictName",
                          "Address",
                          addressId
                        );
                      }
                      if (
                        hasPendingPropertyEdit(
                          "DistrictId",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline("DistrictId", "Address", addressId);
                      }
                    }
                  }}
                />
              ) : shouldShowUserEditButtons &&
                (() => {
                  const addressId = identityCardAddress?.id;
                  return (
                    addressId &&
                    (hasPropertyEdit("DistrictName", "Address", addressId) ||
                      hasPropertyEdit("DistrictId", "Address", addressId))
                  );
                })() ? (
                (() => {
                  const addressId = identityCardAddress?.id;
                  if (!addressId) return null;

                  const districtNameEdit = getPropertyEditInfo(
                    "DistrictName",
                    "Address",
                    addressId
                  );
                  const districtEdit = getPropertyEditInfo(
                    "DistrictId",
                    "Address",
                    addressId
                  );
                  const editInfo = districtNameEdit || districtEdit;

                  if (!editInfo) return null;

                  const previousValue =
                    getOldValue("DistrictName", "Address", addressId) ||
                    districts.find(
                      (d) =>
                        d.id === getOldValue("DistrictId", "Address", addressId)
                    )?.name ||
                    "";

                  return renderSubObjectUserEditIcon(
                    "Address",
                    addressId,
                    editInfo.propertyName,
                    previousValue
                  );
                })()
              ) : null}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued from</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const displayValue =
                    shouldShowAdminEditButtons &&
                    hasPendingPropertyEdit("IDIssuedFrom")
                      ? getOldValue("IDIssuedFrom")
                      : profile?.employee?.idIssuedFrom;

                  return displayValue ? (
                    <>
                      <Translator getString="MOI" /> {displayValue}
                    </>
                  ) : null;
                })()}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("IDIssuedFrom") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.idIssuedFrom}
                  onConfirm={async () => {
                    await handleApprove("IDIssuedFrom");
                  }}
                  onCancel={async () => {
                    await handleDecline("IDIssuedFrom");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("IDIssuedFrom", "Employee") ? (
                renderUserEditIcon("IDIssuedFrom", getOldValue("IDIssuedFrom"))
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Municipality</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const addressId = identityCardAddress?.id;

                  if (
                    shouldShowAdminEditButtons &&
                    addressId &&
                    (hasPendingPropertyEdit(
                      "MunicipalityName",
                      "Address",
                      addressId
                    ) ||
                      hasPendingPropertyEdit(
                        "MunicipalityId",
                        "Address",
                        addressId
                      ))
                  ) {
                    return (
                      getOldValue("MunicipalityName", "Address", addressId) ||
                      municipalities.find(
                        (m) =>
                          m.id ===
                          getOldValue("MunicipalityId", "Address", addressId)
                      )?.name
                    );
                  }

                  return (
                    identityCardAddress?.municipality?.name ??
                    identityCardAddress?.municipalityName
                  );
                })()}
              </ValueLabel>
              {(() => {
                const addressId = identityCardAddress?.id;
                return (
                  addressId &&
                  (hasPendingPropertyEdit(
                    "MunicipalityName",
                    "Address",
                    addressId
                  ) ||
                    hasPendingPropertyEdit(
                      "MunicipalityId",
                      "Address",
                      addressId
                    ))
                );
              })() && shouldShowAdminEditButtons ? (
                <ApproveEditCardHoverIcon
                  newValue={
                    identityCardAddress?.municipality?.name ??
                    identityCardAddress?.municipalityName
                  }
                  onConfirm={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit(
                          "MunicipalityName",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove(
                          "MunicipalityName",
                          "Address",
                          addressId
                        );
                      }
                      if (
                        hasPendingPropertyEdit(
                          "MunicipalityId",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove(
                          "MunicipalityId",
                          "Address",
                          addressId
                        );
                      }
                    }
                  }}
                  onCancel={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit(
                          "MunicipalityName",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline(
                          "MunicipalityName",
                          "Address",
                          addressId
                        );
                      }
                      if (
                        hasPendingPropertyEdit(
                          "MunicipalityId",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline(
                          "MunicipalityId",
                          "Address",
                          addressId
                        );
                      }
                    }
                  }}
                />
              ) : shouldShowUserEditButtons &&
                (() => {
                  const addressId = identityCardAddress?.id;
                  return (
                    addressId &&
                    (hasPropertyEdit(
                      "MunicipalityName",
                      "Address",
                      addressId
                    ) ||
                      hasPropertyEdit("MunicipalityId", "Address", addressId))
                  );
                })() ? (
                (() => {
                  const addressId = identityCardAddress?.id;
                  if (!addressId) return null;

                  const municipalityNameEdit = getPropertyEditInfo(
                    "MunicipalityName",
                    "Address",
                    addressId
                  );
                  const municipalityEdit = getPropertyEditInfo(
                    "MunicipalityId",
                    "Address",
                    addressId
                  );
                  const editInfo = municipalityNameEdit || municipalityEdit;

                  if (!editInfo) return null;

                  const previousValue =
                    getOldValue("MunicipalityName", "Address", addressId) ||
                    municipalities.find(
                      (m) =>
                        m.id ===
                        getOldValue("MunicipalityId", "Address", addressId)
                    )?.name ||
                    "";

                  return renderSubObjectUserEditIcon(
                    "Address",
                    addressId,
                    editInfo.propertyName,
                    previousValue
                  );
                })()
              ) : null}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Citizenship</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("Citizenship")
                  ? getOldValue("Citizenship")
                  : profile?.employee?.citizenship}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("Citizenship") ? (
                <ApproveEditCardHoverIcon
                  newValue={profile?.employee?.citizenship}
                  onConfirm={async () => {
                    await handleApprove("Citizenship");
                  }}
                  onCancel={async () => {
                    await handleDecline("Citizenship");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("Citizenship", "Employee") ? (
                renderUserEditIcon("Citizenship", getOldValue("Citizenship"))
              ) : null}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Address</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {(() => {
                  const addressId = identityCardAddress?.id;

                  if (
                    shouldShowAdminEditButtons &&
                    addressId &&
                    (hasPendingPropertyEdit("Street", "Address", addressId) ||
                      hasPendingPropertyEdit("Block", "Address", addressId) ||
                      hasPendingPropertyEdit(
                        "Apartment",
                        "Address",
                        addressId
                      ) ||
                      hasPendingPropertyEdit(
                        "Neighborhood",
                        "Address",
                        addressId
                      ))
                  ) {
                    return formatAddressFields(
                      hasPendingPropertyEdit(
                        "Neighborhood",
                        "Address",
                        addressId
                      )
                        ? getOldValue("Neighborhood", "Address", addressId)
                        : identityCardAddress?.neighborhood,
                      hasPendingPropertyEdit("Street", "Address", addressId)
                        ? getOldValue("Street", "Address", addressId)
                        : identityCardAddress?.street,
                      hasPendingPropertyEdit("Block", "Address", addressId)
                        ? getOldValue("Block", "Address", addressId)
                        : identityCardAddress?.block,
                      hasPendingPropertyEdit("Apartment", "Address", addressId)
                        ? getOldValue("Apartment", "Address", addressId)
                        : identityCardAddress?.apartment
                    );
                  }
                  return formatFullAddress(identityCardAddress);
                })()}
              </ValueLabel>
              {(() => {
                const addressId = identityCardAddress?.id;
                return (
                  addressId &&
                  (hasPendingPropertyEdit("Street", "Address", addressId) ||
                    hasPendingPropertyEdit("Block", "Address", addressId) ||
                    hasPendingPropertyEdit("Apartment", "Address", addressId) ||
                    hasPendingPropertyEdit(
                      "Neighborhood",
                      "Address",
                      addressId
                    ))
                );
              })() && shouldShowAdminEditButtons ? (
                <ApproveEditCardHoverIcon
                  newValue={formatFullAddress(identityCardAddress)}
                  onConfirm={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit("Street", "Address", addressId)
                      ) {
                        await handleApprove("Street", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit("Block", "Address", addressId)
                      ) {
                        await handleApprove("Block", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit(
                          "Apartment",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove("Apartment", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit(
                          "Neighborhood",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleApprove(
                          "Neighborhood",
                          "Address",
                          addressId
                        );
                      }
                    }
                  }}
                  onCancel={async () => {
                    const addressId = identityCardAddress?.id;
                    if (addressId) {
                      if (
                        hasPendingPropertyEdit("Street", "Address", addressId)
                      ) {
                        await handleDecline("Street", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit("Block", "Address", addressId)
                      ) {
                        await handleDecline("Block", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit(
                          "Apartment",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline("Apartment", "Address", addressId);
                      }
                      if (
                        hasPendingPropertyEdit(
                          "Neighborhood",
                          "Address",
                          addressId
                        )
                      ) {
                        await handleDecline(
                          "Neighborhood",
                          "Address",
                          addressId
                        );
                      }
                    }
                  }}
                />
              ) : shouldShowUserEditButtons &&
                (() => {
                  const addressId = identityCardAddress?.id;
                  return (
                    addressId &&
                    (hasPropertyEdit("Street", "Address", addressId) ||
                      hasPropertyEdit("Block", "Address", addressId) ||
                      hasPropertyEdit("Apartment", "Address", addressId) ||
                      hasPropertyEdit("Neighborhood", "Address", addressId))
                  );
                })() ? (
                (() => {
                  const addressId = identityCardAddress?.id;
                  if (!addressId) return null;

                  const streetEdit = getPropertyEditInfo(
                    "Street",
                    "Address",
                    addressId
                  );
                  const blockEdit = getPropertyEditInfo(
                    "Block",
                    "Address",
                    addressId
                  );
                  const apartmentEdit = getPropertyEditInfo(
                    "Apartment",
                    "Address",
                    addressId
                  );
                  const neighborhoodEdit = getPropertyEditInfo(
                    "Neighborhood",
                    "Address",
                    addressId
                  );
                  const editInfo =
                    streetEdit ||
                    blockEdit ||
                    apartmentEdit ||
                    neighborhoodEdit;

                  if (!editInfo) return null;

                  const previousValue = formatAddressFields(
                    getOldValue("Neighborhood", "Address", addressId),
                    getOldValue("Street", "Address", addressId),
                    getOldValue("Block", "Address", addressId),
                    getOldValue("Apartment", "Address", addressId)
                  );

                  return renderSubObjectUserEditIcon(
                    "Address",
                    addressId,
                    editInfo.propertyName,
                    previousValue
                  );
                })()
              ) : null}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Gender</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowAdminEditButtons &&
                hasPendingPropertyEdit("Gender") ? (
                  getOldValue("Gender") === Genders.Female ? (
                    <Translator getString="Female" />
                  ) : getOldValue("Gender") === Genders.Male ? (
                    <Translator getString="Male" />
                  ) : (
                    ""
                  )
                ) : profile?.employee?.gender === Genders.Female ? (
                  <Translator getString="Female" />
                ) : profile?.employee?.gender === Genders.Male ? (
                  <Translator getString="Male" />
                ) : (
                  ""
                )}
              </ValueLabel>
              {shouldShowAdminEditButtons &&
              hasPendingPropertyEdit("Gender") ? (
                <ApproveEditCardHoverIcon
                  newValue={
                    profile?.employee?.gender === Genders.Female
                      ? translate("Female")
                      : profile?.employee?.gender === Genders.Male
                      ? translate("Male")
                      : ""
                  }
                  onConfirm={async () => {
                    await handleApprove("Gender");
                  }}
                  onCancel={async () => {
                    await handleDecline("Gender");
                  }}
                />
              ) : shouldShowUserEditButtons &&
                hasPropertyEdit("Gender", "Employee") ? (
                renderUserEditIcon(
                  "Gender",
                  getOldValue("Gender") === Genders.Female
                    ? translate("Female")
                    : getOldValue("Gender") === Genders.Male
                    ? translate("Male")
                    : ""
                )
              ) : null}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
      </RightContainer>
    </WrapperContainer>
  );
};

export default PersonalData;
