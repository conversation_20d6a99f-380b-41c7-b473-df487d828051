import { render, waitFor, cleanup } from "@testing-library/react";
import DatesTableContainer from "../DatesTableContainer";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";

// Mock dependencies
const openModalMock = jest.fn();
jest.mock("../../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ openModal: openModalMock }),
}));

jest.mock("../../MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: jest.fn(),
    changeView: jest.fn(),
    isOpen: true,
  }),
}));

jest.mock("../../absences/AbsenceContext", () => ({
  useAbsence: () => ({ setSelectedAbsence: jest.fn() }),
}));

// Mock company context with ability to change company
let mockCompany = { id: "company-1", name: "Company 1" };
jest.mock("../../companies/CompanyContext.tsx", () => ({
  useCompany: () => ({ company: mockCompany }),
}));

// Mock the selectors directly with stable data
const STABLE_PAYROLLS = [
  {
    id: "worktime-1",
    workTimeId: "worktime-1",
    employee: {
      userId: "user-1",
      workTimeId: "emp-1",
      firstName: "John",
      lastName: "Doe",
      email: "",
      egn: "",
    },
    structureLevelId: "lvl-1",
    leaves: [],
  },
];

jest.mock("../../payroll/payrollsActions", () => ({
  onPayrollsLoaded: jest.fn(),
  selectPayrolls: () => ({ payrolls: STABLE_PAYROLLS }),
}));

jest.mock("../../holidays/holidayActions.ts", () => ({
  ...jest.requireActual("../../holidays/holidayActions.ts"),
  selectHolidays: () => ({
    holidays: [
      {
        id: "holiday-1",
        date: new Date(2025, 7, 15).toISOString(),
        type: "OfficialHoliday",
        name: "Test Holiday",
      },
    ],
  }),
}));

jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: (selector: any) => selector(),
}));

jest.mock("../../../components/CalendarComponent/DatesTableView", () => () => (
  <div data-testid="dates-table-view" />
));

afterEach(() => {
  cleanup();
  openModalMock.mockReset();
});

describe("DatesTableContainer State Reset on Company Change", () => {
  const baseProps = {
    selectedPayroll: undefined,
    setSelectedPayroll: jest.fn(),
    selectedEmployee: undefined,
    hoveredEmployee: undefined,
    showMyAbsences: false,
    selectedMonth: 7,
    selectedYear: 2025,
    setSelectedMonth: jest.fn(),
    setSelectedYear: jest.fn(),
    holidays: [],
    highlightedAbsenceId: undefined,
    isFromNotification: false,
  };

  beforeEach(() => {
    cleanup();
    jest.clearAllMocks();
    mockCompany = { id: "company-1", name: "Company 1" };
  });

  test("resets combinedDays when company changes", async () => {
    const { rerender } = render(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Component should render without errors
    expect(
      document.querySelector('[data-testid="dates-table-view"]')
    ).toBeInTheDocument();
  });

  test("resets maxRowsPerWeek when company changes", async () => {
    const { rerender } = render(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Component should render without errors
    expect(
      document.querySelector('[data-testid="dates-table-view"]')
    ).toBeInTheDocument();
  });

  test("resets shownApprovedAbsenceIdRef when company changes", async () => {
    const { rerender } = render(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Component should render without errors
    expect(
      document.querySelector('[data-testid="dates-table-view"]')
    ).toBeInTheDocument();
  });

  test("handles multiple company switches without errors", async () => {
    const { rerender } = render(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Switch to company 2
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Switch to company 3
    mockCompany = { id: "company-3", name: "Company 3" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Component should render without errors
    expect(
      document.querySelector('[data-testid="dates-table-view"]')
    ).toBeInTheDocument();
  });

  test("maintains component functionality after company switch", async () => {
    const { rerender } = render(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(<DatesTableContainer {...baseProps} />);

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="dates-table-view"]')
      ).toBeInTheDocument();
    });

    // Component should render without errors
    expect(
      document.querySelector('[data-testid="dates-table-view"]')
    ).toBeInTheDocument();
  });
});
