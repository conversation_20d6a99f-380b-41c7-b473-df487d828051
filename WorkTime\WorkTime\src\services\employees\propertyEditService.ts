import {
  EditStatus,
  EmployeePropertyEditDTO,
} from "../../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";

export class PropertyEditService {
  static createVirtualEmployeeWithPendingChanges(
    employee: EmployeeD<PERSON>,
    propertyEdits: EmployeePropertyEditDTO[],
    isAdmin: boolean
  ): EmployeeDTO {
    if (isAdmin || !propertyEdits?.length) {
      return employee;
    }

    const virtualEmployee = { ...employee };

    const pendingEmployeeEdits = this.getPendingEditsForObject(
      propertyEdits,
      "Employee",
      employee.workTimeId
    );

    pendingEmployeeEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualEmployee,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualEmployee;
  }

  static getPendingEditsForObject(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName &&
        edit.objectId === objectId &&
        edit.editStatus === EditStatus.Pending
    );
  }

  static getEditsForObject(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) => edit.objectName === objectName && edit.objectId === objectId
    );
  }

  static hasPendingEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): boolean {
    const pendingEdits = this.getPendingEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return pendingEdits.some((edit) => edit.propertyName === propertyName);
  }

  static hasEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): boolean {
    const pendingEdits = this.getEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return pendingEdits.some((edit) => edit.propertyName === propertyName);
  }

  static getPendingEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): EmployeePropertyEditDTO | null {
    const pendingEdits = this.getPendingEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return (
      pendingEdits.find((edit) => edit.propertyName === propertyName) || null
    );
  }

  static getEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): EmployeePropertyEditDTO | null {
    const edits = propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName &&
        edit.objectId === objectId &&
        edit.propertyName === propertyName
    );
    return (
      edits.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0] || null
    );
  }

  static getOldValueForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): string | null {
    const edit = this.getPendingEditForProperty(
      propertyEdits,
      objectName,
      objectId,
      propertyName
    );
    return edit?.oldValue || null;
  }

  private static applyPropertyChange(
    obj: any,
    propertyName: string,
    newValue: string
  ): void {
    try {
      const mappedPropertyName = this.mapPropertyName(propertyName);

      let parsedValue: any;
      try {
        parsedValue = JSON.parse(newValue);
      } catch {
        parsedValue = newValue;
      }

      obj[mappedPropertyName] = parsedValue;
    } catch (error) {
      console.warn(
        `Failed to apply property change for ${propertyName}:`,
        error
      );
    }
  }

  static toCamelCase(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1);
  }

  static mapPropertyName(propertyName: string): string {
    const propertyMappings: { [key: string]: string } = {
      IDNumber: "idNumber",
      IDIssueDate: "idIssueDate",
      IDIssuedFrom: "idIssuedFrom",
      EGN: "egn",
      IBAN: "iban",
    };

    return propertyMappings[propertyName] || this.toCamelCase(propertyName);
  }

  static hasPendingEdits(propertyEdits: EmployeePropertyEditDTO[]): boolean {
    return (
      propertyEdits?.some((edit) => edit.editStatus === EditStatus.Pending) ||
      false
    );
  }

  static getPendingEditsCount(
    propertyEdits: EmployeePropertyEditDTO[]
  ): number {
    return (
      propertyEdits?.filter((edit) => edit.editStatus === EditStatus.Pending)
        .length || 0
    );
  }

  static getPendingEditsForObjectType(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName && edit.editStatus === EditStatus.Pending
    );
  }

  static hasPendingEditsForObjectType(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): boolean {
    return (
      this.getPendingEditsForObjectType(propertyEdits, objectName).length > 0
    );
  }

  static getObjectIdsWithPendingEdits(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): string[] {
    const pendingEdits = this.getPendingEditsForObjectType(
      propertyEdits,
      objectName
    );
    const uniqueIds = [...new Set(pendingEdits.map((edit) => edit.objectId))];
    return uniqueIds;
  }

  static createVirtualAddressWithPendingChanges(
    originalAddress: any,
    propertyEdits: EmployeePropertyEditDTO[],
    addressId: string,
    isAdmin: boolean
  ): any {
    if (isAdmin || !propertyEdits?.length) {
      return originalAddress;
    }

    const virtualAddress = { ...originalAddress };

    const pendingAddressEdits = this.getPendingEditsForObject(
      propertyEdits,
      "Address",
      addressId
    );

    pendingAddressEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualAddress,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualAddress;
  }

  static createVirtualBankAccountWithPendingChanges(
    originalBankAccount: any,
    propertyEdits: EmployeePropertyEditDTO[],
    bankAccountId: string,
    isAdmin: boolean
  ): any {
    if (isAdmin || !propertyEdits?.length) {
      return originalBankAccount;
    }

    const virtualBankAccount = { ...originalBankAccount };

    const pendingBankAccountEdits = this.getPendingEditsForObject(
      propertyEdits,
      "BankAccount",
      bankAccountId
    );

    pendingBankAccountEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualBankAccount,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualBankAccount;
  }
}
