import { render, waitFor, cleanup } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import Attendance from "../Attendance";
import { useCompany } from "../../companies/CompanyContext";
import { useUserEmployee } from "../../UserEmployeeContext";

// Mock the child components
jest.mock("../AttendancesRightView", () => () => (
  <div data-testid="attendances-right-view" />
));
jest.mock("../DatesTableContainer", () => () => (
  <div data-testid="dates-table-container" />
));

// Mock Redux hooks
jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: () => ({ holidays: [] }),
}));

// Mock holiday actions
jest.mock("../../holidays/holidayActions", () => ({
  onHolidaysLoaded: jest.fn(),
  selectHolidays: () => ({ holidays: [] }),
}));

// Mock user employee context
const mockUserEmployee = {
  payrolls: [
    {
      id: "payroll-1",
      workTimeId: "worktime-1",
      employee: {
        userId: "user-1",
        workTimeId: "emp-1",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        egn: "1234567890",
      },
      structureLevelId: "lvl-1",
      leaves: [],
      annexPayrolls: [],
    },
  ],
  permissions: ["Attendances.Write"],
};

jest.mock("../../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    userEmployee: mockUserEmployee,
  }),
}));

// Mock company context with ability to change company
let mockCompany = { id: "company-1", name: "Company 1" };
const mockSetCompany = jest.fn();
const mockResetCompany = jest.fn();

jest.mock("../../companies/CompanyContext", () => ({
  useCompany: () => ({
    company: mockCompany,
    setCompany: mockSetCompany,
    resetCompany: mockResetCompany,
  }),
}));

// Mock search params
const mockSetSearchParams = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useSearchParams: () => [new URLSearchParams(""), mockSetSearchParams],
}));

describe("Attendance Company Switching", () => {
  beforeEach(() => {
    cleanup();
    jest.clearAllMocks();
    mockCompany = { id: "company-1", name: "Company 1" };
  });

  const renderAttendance = (companyId: string = "company-1") => {
    return render(
      <MemoryRouter initialEntries={[`/${companyId}/attendance`]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );
  };

  test("resets local state when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    // Wait for initial render
    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Simulate company change by updating the mock
    mockCompany = { id: "company-2", name: "Company 2" };

    // Rerender with new company
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    // The component should re-render with the new company
    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });
  });

  test("component renders without errors when company changes", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Change company
    mockCompany = { id: "company-2", name: "Company 2" };

    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
      expect(
        document.querySelector('[data-testid="dates-table-container"]')
      ).toBeInTheDocument();
      expect(
        document.querySelector('[data-testid="attendances-right-view"]')
      ).toBeInTheDocument();
    });
  });

  test("handles multiple company switches correctly", async () => {
    const { rerender } = renderAttendance("company-1");

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Switch to company 2
    mockCompany = { id: "company-2", name: "Company 2" };
    rerender(
      <MemoryRouter initialEntries={["/company-2/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });

    // Switch to company 3
    mockCompany = { id: "company-3", name: "Company 3" };
    rerender(
      <MemoryRouter initialEntries={["/company-3/attendance"]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        document.querySelector('[data-testid="main-window-container"]')
      ).toBeInTheDocument();
    });
  });
});
