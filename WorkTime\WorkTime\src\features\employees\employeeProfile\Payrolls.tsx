import React, { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import profileWoman from "../../../assets/images/profile/WomanProfilePic.svg";
import Combobox from "../../../components/Combobox/Combobox";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import Label from "../../../components/Inputs/Label";
import { selectEmployees } from "../../employees/employeesActions";
import { PayrollSummaryDTO } from "../../../models/DTOs/payrolls/PayrollSummaryDTO";
import { PayrollsPositionNamesDTO } from "../../../models/DTOs/payrolls/PayrollsPositionNamesDTO";
import {
  getEmployeePayrollsPositionNames,
  getPayrollData,
} from "../../../services/payrollsService/payrollsService";
import { useCompany } from "../../companies/CompanyContext";
import LaborContractPayrollView from "./payrollViews/LaborContractPayrollView";
import SelfInsuredPayrollView from "./payrollViews/SelfInsuredPayrollView";
import CivilContractPayrollView from "./payrollViews/CivilContractPayrollView";
import ManagementContractPayrollView from "./payrollViews/ManagementContractPayrollView";
import EmployeePayrollView from "./payrollViews/EmployeePayrollView";
import DividendPayrollView from "./payrollViews/DividendPayrollView";
import ExternalPayrollView from "./payrollViews/ExternalPayrollView";
import { useAppSelector } from "../../../app/hooks";
import { Genders } from "../../../constants/enum";

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0.5rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const ContainersWrapper = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 30%;
  gap: 2rem;
  align-items: flex-start;
`;

const MainContentWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 1rem;
`;

const TopContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  gap: 1rem;
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 0;
  height: 4rem;
  width: 4rem;
`;

const EmployeeInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
`;

const EmployeeName = styled(Label)`
  text-align: left;
  font-weight: bold;
  opacity: 1;
  font-size: 1rem;
  word-wrap: normal;
  margin: 0;
`;

const Payrolls = ({ employeeName }: { employeeName: string }) => {
  const employeesState = useAppSelector(selectEmployees) as any;
  const profile = employeesState.currentProfile;
  const [payrollId, setPayrollId] = useState(profile.payrollPersonalData.id);
  const [selectedPayroll, setSelectedPayroll] = useState<PayrollSummaryDTO>();
  const [contractType, setContractType] = useState("Трудов договор");
  const { company } = useCompany();

  const [selectedPayrollPosition, setSelectedPayrollPosition] =
    useState<PayrollsPositionNamesDTO>();
  const [payrollPositions, setPayrollPositions] = useState<
    PayrollsPositionNamesDTO[]
  >([]);

  useEffect(() => {
    const fetchPayrollPossition = async () => {
      try {
        const payrollPositionNames = await getEmployeePayrollsPositionNames(
          company.id,
          profile.employee.workTimeId
        );
        setPayrollPositions(payrollPositionNames);

        setSelectedPayrollPosition(
          payrollPositionNames.find((p) => p.payrollId === payrollId)
        );
      } catch (error) {
        console.error(error);
      }
    };
    if (profile?.employee.workTimeId) {
      fetchPayrollPossition();
    }
  }, []);
  const handlePayrollPositionChange = (chosenOption: string) => {
    if (!chosenOption) return;
    const selectedPosition = payrollPositions.find(
      (p) => p.positionName === chosenOption
    );
    setSelectedPayrollPosition(selectedPosition);

    if (selectedPosition) {
      const newPayrollId = selectedPosition.payrollId;
      if (newPayrollId !== payrollId) {
        setPayrollId(newPayrollId);
      }
    }
  };

  useEffect(() => {
    const fetchPayrollData = async () => {
      try {
        const payrollData = await getPayrollData(company.id, payrollId);
        setSelectedPayroll(payrollData);

        if (payrollData?.contractType?.name) {
          setContractType(payrollData.contractType.name);
        }
      } catch (error) {
        console.error("Error fetching payroll data:", error);
      }
    };

    if (payrollId) {
      fetchPayrollData();
    }
  }, [payrollId, company.id]);

  const renderContractView = () => {
    if (!selectedPayroll) return null;

    switch (contractType) {
      case "Трудов договор":
        return <LaborContractPayrollView payrollData={selectedPayroll} />;
      case "Самоосигуряващо се лице":
        return <SelfInsuredPayrollView payrollData={selectedPayroll} />;
      case "Граждански договор":
        return <CivilContractPayrollView payrollData={selectedPayroll} />;
      case "Договор за управление":
        return <ManagementContractPayrollView payrollData={selectedPayroll} />;
      case "Наемодател":
        return <EmployeePayrollView payrollData={selectedPayroll} />;
      case "Дивидент":
        return <DividendPayrollView payrollData={selectedPayroll} />;
      case "Външен":
        return <ExternalPayrollView payrollData={selectedPayroll} />;
      default:
        return <LaborContractPayrollView payrollData={selectedPayroll} />;
    }
  };

  return (
    <WrapperContainer>
      <ContainersWrapper>
        <MainContentWrapper>
          <TopContainer>
            <EmployeeImage
              src={
                profile.employee.gender === Genders.Male
                  ? profileMan
                  : profileWoman
              }
              alt="Employee's profile picture"
              data-testid="profile-employee-image"
            />
            <EmployeeInfoContainer>
              <EmployeeName>{employeeName}</EmployeeName>
              {selectedPayrollPosition && (
                <Combobox
                  key={`main-${selectedPayrollPosition?.payrollId}`}
                  data-testid="payroll-position-combobox"
                  options={payrollPositions.map(
                    (payroll: PayrollsPositionNamesDTO) =>
                      payroll.positionName || ""
                  )}
                  initialSelectedItem={selectedPayrollPosition?.positionName}
                  onChange={handlePayrollPositionChange}
                />
              )}
            </EmployeeInfoContainer>
          </TopContainer>

          {renderContractView()}
        </MainContentWrapper>
      </ContainersWrapper>
    </WrapperContainer>
  );
};

export default Payrolls;
