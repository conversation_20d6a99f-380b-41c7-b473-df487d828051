using MediatR;
using Gateway.Common.Globals;

namespace WorkTimeApi.Infrastructure.MediatR
{
    public sealed class BackgroundNotificationPublisher(ILogger<BackgroundNotificationPublisher> logger,
        IServiceScopeFactory serviceScopeFactory) : INotificationPublisher
    {
        public async Task Publish(IEnumerable<NotificationHandlerExecutor> handlerExecutors,
            INotification notification,
            CancellationToken cancellationToken)
        {
            var currentScope = serviceScopeFactory.CreateScope();
            var currentGlobalUser = currentScope.ServiceProvider.GetService<GlobalUser>();
            var currentGlobalEmployee = currentScope.ServiceProvider.GetService<GlobalEmployee>();

            var capturedUserData = currentGlobalUser;

            var capturedEmployeeData = currentGlobalEmployee;

            currentScope.Dispose();

            _ = ExecuteHandlersAsync(handlerExecutors, notification, capturedUserData, capturedEmployeeData, cancellationToken);
        }

        private async Task ExecuteHandlersAsync(IEnumerable<NotificationHandlerExecutor> handlerExecutors,
            INotification notification,
            GlobalUser? capturedUserData,
            GlobalEmployee? capturedEmployeeData,
            CancellationToken cancellationToken)
        {
            foreach (var handlerExecutor in handlerExecutors)
            {
                try
                {
                    using var handlerScope = serviceScopeFactory.CreateScope();

                    if (capturedUserData != null)
                    {
                        var globalUser = handlerScope.ServiceProvider.GetService<GlobalUser>();
                        if (globalUser != null)
                        {
                            globalUser.Id = capturedUserData.Id;
                            globalUser.FirstName = capturedUserData.FirstName;
                            globalUser.LastName = capturedUserData.LastName;
                            globalUser.Email = capturedUserData.Email;
                        }
                    }

                    if (capturedEmployeeData != null)
                    {
                        var globalEmployee = handlerScope.ServiceProvider.GetService<GlobalEmployee>();
                        if (globalEmployee != null)
                        {
                            globalEmployee.Id = capturedEmployeeData.Id;
                            globalEmployee.CompanyId = capturedEmployeeData.CompanyId;
                            globalEmployee.Permissions = capturedEmployeeData.Permissions;
                        }
                    }

                    await handlerExecutor.HandlerCallback(notification, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex,
                        "Unhandled exception in background notification handler {HandlerType} for {NotificationType}.",
                        handlerExecutor.HandlerInstance.GetType().FullName,
                        notification.GetType().FullName);
                }
            }
        }
    }
}
