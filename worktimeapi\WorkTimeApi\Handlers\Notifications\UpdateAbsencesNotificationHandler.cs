﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Notifications
{
    public class UpdateAbsencesNotificationHandler(ISignalRNotificationService signalRNotificationService,
        IAbsencesService absencesService, IMediator mediator)
        : INotificationHandler<UpdateAbsencesNotification>
    {
        public async Task Handle(UpdateAbsencesNotification notification, CancellationToken cancellationToken)
        {
            var deletedAbsences = notification.Payload.Where(p => p.Status == Common.Enums.AbsenceStatus.DeletedByAdmin);
            var updatedAbsences = notification.Payload.Where(p => p.Status != Common.Enums.AbsenceStatus.DeletedByAdmin);
            if (updatedAbsences != null && updatedAbsences.Any())
            {
                var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(updatedAbsences.FirstOrDefault().Id, updatedAbsences.FirstOrDefault().IsHospital);
                var pushNotifications = signalRNotificationService.BroadcastForCompanyAsync(notification);

                await Task.WhenAll(pushNotifications);
            }

            foreach (var deletedAbsence in deletedAbsences)
            {
                var notificationDeleteByAdmin = new AbsenceDeletedByAdminNotification(deletedAbsence, notification.CompanyId, notification.UserId, notification.CreatorName, notification.EmployeeId);
                mediator.Publish(notificationDeleteByAdmin, cancellationToken);
            }
        }
    }
}
